<?php

use App\Filament\Pages\View3DModel;
use App\Http\Controllers\KiriWebhookController;
use App\Http\Controllers\Product3DModelController;
use Illuminate\Support\Facades\Route;

// Route::get('/', function () {
//     return view('welcome');
// });

Route::get('/', function () {
    return view('Home');
});

Route::get('/login', function () {
    return view('Login');
});

Route::get('/register', function () {
    return view('Register');
});

Route::get('/product-overview', function () {
    return view('ProductOverview');
});

Route::get('/shopcenter', function () {
    return view('ShopCenter');
});

Route::get('/productlist', function () {
    return view('ProductList');
});

Route::get('/account', function () {
    return view('AccountPage');
});

Route::get('/shops', function () {
    return view('Shops');
});

Route::post('/webhooks/kiri-model-ready', [KiriWebhookController::class, 'modelReady'])->name('webhooks.kiri-model-ready');

Route::post('/save-clipping/{id}', [Product3DModelController::class, 'saveClipping']);

Route::get('view3-d-model/{id}', View3DModel::class)
    ->middleware(['web', 'auth'])
    ->name('view-3d-model');
