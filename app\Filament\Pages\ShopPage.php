<?php

namespace App\Filament\Pages;

use App\Models\Products;
use Filament\Actions\Action;
use Filament\Actions\BulkActionGroup;
use Filament\Actions\CreateAction;
use Filament\Actions\DeleteBulkAction;
use Filament\Actions\EditAction;
use Filament\Actions\ViewAction;
use Filament\Pages\Page;
use Filament\Support\Icons\Heroicon;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Concerns\InteractsWithTable;
use Filament\Tables\Contracts\HasTable;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Facades\Auth;
use BackedEnum;

class ShopPage extends Page implements HasTable
{
    use InteractsWithTable;

    protected static string|BackedEnum|null $navigationIcon = Heroicon::OutlinedShoppingBag;

    protected string $view = 'filament.pages.shop-page';

    public static function canAccess(): bool
    {
        return Auth::user()?->isAdmin();
    }

    public function getTableQuery(): Builder
    {
        return Products::query()->where('user_id', auth()->id());
    }

    public function getHeaderActions(): array
    {
        return [
            Action::make('addProduct')
                ->label('Add products to shop')
                ->icon('heroicon-o-plus')
                ->getUrl(AddProductToShopPage::class),
        ];
    }

    public function table(Table $table): Table
    {
        return $table
            ->query(Products::query()->where('user_id', auth()->id()))
            ->modifyQueryUsing(fn(Builder $query) => $query->where('visibility', 'Yes'))
            ->columns([
                TextColumn::make('name')->label('Product Name')->searchable(),
                TextColumn::make('type')->label('Product Type')->searchable(),
                TextColumn::make('subtype')->label('Style')->searchable(),
            ])
            ->filters([
                //
            ])
            ->actions([
                ViewAction::make(),
                EditAction::make(),
            ])
            ->bulkActions([
                BulkActionGroup::make([
                    DeleteBulkAction::make(),
                ]),
            ]);
    }
}
